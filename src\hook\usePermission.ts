import { ref, onMounted } from 'vue'
import { checkedBtnPermission } from '@/utils'

// 声明全局调试对象类型
declare global {
  interface Window {
    __PERMISSION_DEBUG__: any
  }
}

export const usePermission = (code?: string) => {
  const btnPermission = ref<any>({})

  const fetchPermissions = async () => {
    console.log('🔑 [usePermission] 开始获取权限，code:', code)

    // 添加到全局调试对象
    if (typeof window !== 'undefined' && window.__PERMISSION_DEBUG__) {
      window.__PERMISSION_DEBUG__.addLog('USE_PERMISSION_START', {
        code,
        timestamp: new Date().toISOString(),
        currentBtnPermissionValue: btnPermission.value
      })
    }

    try {
      const permissions = await checkedBtnPermission(code)
      btnPermission.value = permissions

      // 添加到全局调试对象
      if (typeof window !== 'undefined' && window.__PERMISSION_DEBUG__) {
        window.__PERMISSION_DEBUG__.btnPermissionValue = permissions
        window.__PERMISSION_DEBUG__.addLog('USE_PERMISSION_SUCCESS', {
          code,
          permissions,
          permissionCount: Object.keys(permissions).length,
          permissionIds: Object.keys(permissions),
          btnPermissionRefValue: btnPermission.value,
          isRefValueSameAsPermissions: JSON.stringify(btnPermission.value) === JSON.stringify(permissions)
        })
      }

      console.log('🔑 [usePermission] 权限获取成功:', permissions)
      console.log('🔑 [usePermission] btnPermission.value 已更新:', btnPermission.value)

    } catch (error) {
      console.error('🔑 [usePermission] 权限获取失败:', error)

      // 添加到全局调试对象
      if (typeof window !== 'undefined' && window.__PERMISSION_DEBUG__) {
        window.__PERMISSION_DEBUG__.addLog('USE_PERMISSION_ERROR', {
          code,
          error: error.message,
          stack: error.stack
        })
      }
    }
  }

  onMounted(() => {
    console.log('🔑 [usePermission] onMounted 触发')

    // 添加到全局调试对象
    if (typeof window !== 'undefined' && window.__PERMISSION_DEBUG__) {
      window.__PERMISSION_DEBUG__.addLog('USE_PERMISSION_MOUNTED', {
        code,
        timestamp: new Date().toISOString()
      })
    }

    fetchPermissions()
  })

  return {
    btnPermission,
  }
}
