import { ref, onMounted } from 'vue'
import { checkedBtnPermission } from '@/utils'

export const usePermission = (code?: string) => {
  const btnPermission = ref<any>({})

  const fetchPermissions = async () => {
    console.log('🔑 [usePermission] 开始获取权限，code:', code)
    try {
      const permissions = await checkedBtnPermission(code)
      btnPermission.value = permissions
      console.log('🔑 [usePermission] 权限获取成功:', permissions)
    } catch (error) {
      console.error('🔑 [usePermission] 权限获取失败:', error)
    }
  }

  onMounted(() => {
    console.log('🔑 [usePermission] onMounted 触发')
    fetchPermissions()
  })

  return {
    btnPermission,
  }
}
